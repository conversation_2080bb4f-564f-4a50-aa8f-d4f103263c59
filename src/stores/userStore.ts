import { create } from 'zustand'
import { User } from 'firebase/auth'
import { persist } from 'zustand/middleware'
import { UserProfile } from '../types/user' // Added import

interface UserState {
  user: User | null
  userProfile: UserProfile | null // Added userProfile
  isLoading: boolean
  lastFetched: number | null // Added lastFetched timestamp
  setUser: (user: User | null) => void
  setUserProfile: (profile: UserProfile | null) => void // Added setUserProfile
  setLoading: (loading: boolean) => void
  setLastFetched: (timestamp: number | null) => void // Added setLastFetched
}

export const useUserStore = create<UserState>()(
  persist(
    (set) => ({
      user: null,
      userProfile: null, // Initialized userProfile
      isLoading: true,
      lastFetched: null, // Initialized lastFetched
      setUser: (user) => set({ user }),
      setUserProfile: (profile) => set({ userProfile: profile }), // Implemented setUserProfile
      setLoading: (loading) => set({ isLoading: loading }),
      setLastFetched: (timestamp) => set({ lastFetched: timestamp }), // Implemented setLastFetched
    }),
    {
      name: 'user-storage',
    }
  )
)